'use client'

import { useThemeStore, Theme } from '@/stores'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '@/components/ui/Dropdown'

export function ThemeController() {
  const { theme, setTheme } = useThemeStore()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className="btn btn-circle btn-outline border-base-300 transition-colors focus:bg-base-200 focus:outline-none"
          aria-label={`Current theme: ${theme}. Click to change theme.`}
          title={`Current theme: ${theme}`}
        >
          <span
            className="icon-[solar--palette-round-bold-duotone]"
            aria-hidden="true"
          />
        </button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        sideOffset={16}
        aria-label="Theme selection menu"
      >
        <DropdownMenuRadioGroup value={theme} onValueChange={(value) => setTheme(value as Theme)}>
          <DropdownMenuRadioItem
            value="light"
            className="flex items-center gap-3 px-3 py-2.5 cursor-pointer focus:bg-base-200 hover:bg-base-200 data-[state=checked]:bg-primary data-[state=checked]:text-primary-content data-[state=checked]:hover:bg-primary data-[state=checked]:focus:bg-primary"
            aria-label="Light theme"
          >
            <span
              className={`icon-[solar--sun-fog-bold-duotone] ${
                theme === 'light' ? 'text-current' : 'text-amber-500'
              }`}
              aria-hidden="true"
            />
            <span className="font-medium text-sm">Light</span>
          </DropdownMenuRadioItem>

          <DropdownMenuRadioItem
            value="dark"
            className="flex items-center gap-3 px-3 py-2.5 cursor-pointer focus:bg-base-200 hover:bg-base-200 data-[state=checked]:bg-primary data-[state=checked]:text-primary-content data-[state=checked]:hover:bg-primary data-[state=checked]:focus:bg-primary"
            aria-label="Dark theme"
          >
            <span
              className={`icon-[solar--moon-stars-bold-duotone] ${
                theme === 'dark' ? 'text-current' : 'text-violet-500'
              }`}
              aria-hidden="true"
            />
            <span className="font-medium text-sm">Dark</span>
          </DropdownMenuRadioItem>

          <DropdownMenuRadioItem
            value="system"
            className="flex items-center gap-3 px-3 py-2.5 cursor-pointer focus:bg-base-200 hover:bg-base-200 data-[state=checked]:bg-primary data-[state=checked]:text-primary-content data-[state=checked]:hover:bg-primary data-[state=checked]:focus:bg-primary"
            aria-label="System theme"
          >
            <span
              className={`icon-[solar--monitor-bold-duotone] ${
                theme === 'system' ? 'text-current' : 'text-base-content'
              }`}
              aria-hidden="true"
            />
            <span className="font-medium text-sm">System</span>
          </DropdownMenuRadioItem>
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
